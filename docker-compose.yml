services:
  nginx:
    image: nginx:alpine
    container_name: meka_nginx
    ports:
      - "8080:80"
    volumes:
      - ./src:/var/www/html
      - ./docker/nginx/default.conf:/etc/nginx/conf.d/default.conf
    depends_on:
      - php
    networks:
      - meka_network

  php:
    build:
      context: .
      dockerfile: ./docker/php/Dockerfile
    container_name: meka_php
    volumes:
      - ./src:/var/www/html
    networks:
      - meka_network
    environment:
      - PHP_INI_SCAN_DIR=/usr/local/etc/php/conf.d

networks:
  meka_network:
    driver: bridge

volumes:
  meka_data: