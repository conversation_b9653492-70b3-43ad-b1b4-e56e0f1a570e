FROM php:8.2-fpm-alpine

# Sistem bağımlılıklarını yükle
RUN apk add --no-cache \
    git \
    curl \
    libpng-dev \
    libxml2-dev \
    zip \
    unzip \
    oniguruma-dev \
    icu-dev

# PHP eklentilerini yükle
RUN docker-php-ext-configure intl && \
    docker-php-ext-install \
    pdo_mysql \
    mbstring \
    exif \
    pcntl \
    bcmath \
    gd \
    intl

# Composer'ı yükle
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# Çalışma dizinini ayarla
WORKDIR /var/www/html

# Kullanıcı ve grup ayarları
RUN addgroup -g 1000 -S www && \
    adduser -u 1000 -S www -G www

# Dizin izinlerini ayarla
RUN chown -R www:www /var/www/html

# www kullanıcısını kullan
USER www

# PHP-FPM'i başlat
CMD ["php-fpm"]