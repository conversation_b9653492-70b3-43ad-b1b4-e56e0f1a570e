# Asya Fresh Website - asyafresh.com Kopyası

Bu proje, asyafresh.com sitesinin birebir kopyasıdır. Docker, PHP, Nginx ve Bootstrap kullanılarak geliştirilmiştir.

## 🚀 Özellikler

- **Docker** altyapısı ile kolay deployment
- **PHP 8.2** backend
- **Nginx** web server
- **Bootstrap 5.3** responsive tasarım
- **Responsive** mobile uyumlu tasarım
- **İletişim formu** - dosya yükleme, ürün/ülke seçimi
- **CSRF koruması**
- **Modern** ve temiz arayüz

## 📁 Proje <PERSON>

```
meka01/
├── docker/
│   ├── nginx/
│   │   └── default.conf       # Nginx konfigürasyonu
│   └── php/
│       └── Dockerfile         # PHP-FPM Dockerfile
├── src/
│   ├── assets/
│   │   ├── css/
│   │   │   └── style.css      # Özel CSS dosyası
│   │   ├── js/                # JavaScript dosyaları
│   │   └── images/            # Görsel dosyaları
│   ├── includes/              # PHP include dosyaları
│   ├── uploads/               # Yüklenen dosyalar
│   ├── config.php             # PHP konfigürasyonu
│   ├── index.php              # Ana sayfa
│   └── contact-form.php       # İletişim formu işleyicisi
├── docker-compose.yml         # Docker Compose konfigürasyonu
└── README.md                  # Bu dosya
```

## 🐳 Docker ile Çalıştırma

### Ön Gereksinimler
- Docker ve Docker Compose yüklü olmalı

### Kurulum ve Çalıştırma

1. **Projeyi klonlayın**
   ```bash
   git clone <repository-url>
   cd meka01
   ```

2. **Docker container'ları çalıştırın**
   ```bash
   docker-compose up --build -d
   ```

3. **Tarayıcıda açın**
   ```
   http://localhost:8080
   ```

### Container'ları Durdurma
```bash
docker-compose down
```

## 🔧 Geliştirme Ortamı

### PHP Built-in Server ile Test (Docker olmadan)
```bash
cd src
php -S localhost:8080
```

### Log'ları İzleme
```bash
docker-compose logs -f
```

## 📋 Site Bölümleri

1. **Ana Sayfa (Hero Section)**
   - Asya Fresh tanıtımı
   - Call-to-action butonları

2. **Ürünler**
   - Pomegranate (Nar)
   - Cherry (Kiraz)  
   - Nectarine and Peach (Şeftali/Nektarin)

3. **Hizmetler**
   - Production (Üretim)
   - Logistic (Lojistik)
   - Wholesale (Toptan Satış)

4. **Hakkımızda**
   - Şirket hikayesi
   - Değerler ve misyon

5. **İletişim Formu**
   - Ürün seçimi (dropdown)
   - Ülke seçimi (dropdown)
   - Dosya yükleme özelliği
   - CSRF koruması

6. **Haberler**
   - Site yenileme haberi
   - Tarım üretimi haberi

7. **Footer**
   - Hızlı linkler
   - Sosyal medya
   - Telif hakkı

## 🎨 Tasarım Özellikleri

- **Renk Paleti**: Yeşil tonları (tarım teması)
- **Typography**: Arial font family
- **Responsive**: Mobile-first yaklaşım
- **Modern**: Bootstrap 5.3 komponentleri
- **Interactive**: Smooth scrolling ve hover efektleri

## 🔒 Güvenlik

- CSRF token koruması
- HTML character escaping
- Dosya yükleme güvenliği
- Input validation

## 📱 Responsive Tasarım

- **Desktop**: Full layout
- **Tablet**: Adapted grid system
- **Mobile**: Collapsed navigation, stacked content

## 🛠️ Teknik Detaylar

### PHP Konfigürasyonu
- PHP 8.2-FPM
- Gerekli eklentiler: gd, mbstring, intl
- Session management
- Error handling

### Nginx Konfigürasyonu
- Gzip compression
- Security headers
- Static file caching
- PHP-FPM integration

### CSS Framework
- Bootstrap 5.3
- Font Awesome icons
- Custom CSS variables
- Responsive utilities

## 🚢 Production Deployment

### Server Gereksinimleri
- Docker ve Docker Compose
- 1GB+ RAM
- 10GB+ disk space

### Domain Konfigürasyonu
1. `nginx/default.conf` dosyasında `server_name` güncelle
2. `config.php` dosyasında `SITE_URL` güncelle
3. SSL certificate ekle (Let's Encrypt önerilir)

### Environment Variables
```bash
# .env dosyası oluştur
SITE_URL=https://yourdomain.com
DB_HOST=database
DB_NAME=asyafresh
DB_USER=root
DB_PASS=password
```

## 📞 İletişim ve Destek

Bu proje asyafresh.com sitesinin eğitim amaçlı kopyasıdır.

## 📄 Lisans

Bu proje eğitim amaçlıdır. Ticari kullanım için gerekli izinler alınmalıdır.

---

**Not**: Gerçek görseller `src/assets/images/` klasörüne eklenerek placeholder görsellerin yerini alabilir.