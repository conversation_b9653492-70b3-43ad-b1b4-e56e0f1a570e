<?php
// Hata raporlama ayarları
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Zaman dilimi ayarı
date_default_timezone_set('Europe/Istanbul');

// Karakter kodlaması
mb_internal_encoding('UTF-8');

// Session ayarları
session_start();

// Site konfigürasyonu
define('SITE_URL', 'http://localhost:8080');
define('SITE_NAME', 'Asya Fresh');
define('SITE_DESCRIPTION', 'Turkish Fresh Fruits and Vegetables');

// Yardımcı fonksiyonlar
function escape($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}

function base_url($path = '') {
    return SITE_URL . '/' . ltrim($path, '/');
}

function asset_url($path) {
    return base_url('assets/' . ltrim($path, '/'));
}

// CSRF koruması için token oluştur
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

function get_csrf_token() {
    return $_SESSION['csrf_token'];
}

function verify_csrf_token($token) {
    return hash_equals($_SESSION['csrf_token'], $token);
}
?>