<?php require_once 'config.php'; ?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo SITE_NAME; ?> | <?php echo SITE_DESCRIPTION; ?></title>
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="<?php echo asset_url('css/style.css'); ?>" rel="stylesheet">
    
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="<?php echo asset_url('images/favicon1.jpg'); ?>">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg fixed-top modern-navbar">
        <div class="container">
            <a class="navbar-brand modern-brand" href="<?php echo base_url(); ?>">
                <div class="brand-icon">
                    <i class="fas fa-leaf"></i>
                </div>
                <span class="brand-text">Meka Fresh</span>
            </a>

            <button class="navbar-toggler modern-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span></span>
                <span></span>
                <span></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="#media">Gallery</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#products">Products</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#services">Services</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#about">About</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link contact-btn" href="#contact">Contact</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Scroll Progress Bar -->
    <div class="scroll-progress"></div>

    <!-- Media Gallery Section -->
    <section id="media" class="fullscreen-gallery">
        <div class="gallery-container">
            <div class="gallery-slides">
                <div class="gallery-slide active">
                    <img src="<?php echo asset_url('images/mekaslide2.jpg'); ?>" alt="Gallery Image 1">
                </div>
            </div>
        </div>
    </section>

    <!-- Products Section -->
    <section id="products" class="py-5">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="display-5 fw-bold text-success">Our products</h2>
                <p class="lead text-muted">Fresh and high-quality fruits from Turkey</p>
            </div>

            <div class="row g-4">
                <!-- Pomegranate -->
                <div class="col-lg-4 col-md-6">
                    <div class="card product-card h-100">
                        <img src="<?php echo asset_url('images/peach-and-nectarin-2-.jpg'); ?>" class="card-img-top" alt="Pomegranate">
                        <div class="card-body text-center">
                            <h5 class="card-title fw-bold">Pomegranate</h5>
                            <p class="card-text text-muted">Hijaz, Wonderful</p>
                            <a href="#" class="btn btn-outline-primary">Review</a>
                        </div>
                    </div>
                </div>

                <!-- Cherry -->
                <div class="col-lg-4 col-md-6">
                    <div class="card product-card h-100">
                        <img src="<?php echo asset_url('images/peach-and-nectarin-2-.jpg'); ?>" class="card-img-top" alt="Cherry">
                        <div class="card-body text-center">
                            <h5 class="card-title fw-bold">Cherry</h5>
                            <p class="card-text text-muted">Napoleon Z900, Regina, Sweetheart</p>
                            <a href="#" class="btn btn-outline-primary">Review</a>
                        </div>
                    </div>
                </div>

                <!-- Nectarine and Peach -->
                <div class="col-lg-4 col-md-6">
                    <div class="card product-card h-100">
                        <img src="<?php echo asset_url('images/peach-and-nectarin-2-.jpg'); ?>" class="card-img-top" alt="Nectarine and Peach">
                        <div class="card-body text-center">
                            <h5 class="card-title fw-bold">Nectarin and Peach</h5>
                            <p class="card-text text-muted">Earlier nectarines and peaches</p>
                            <a href="#" class="btn btn-outline-primary">Review</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- Services Section -->
    <section id="services" class="py-5 bg-light">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="display-5 fw-bold text-success">Our Services</h2>
                <p class="lead text-muted">Comprehensive solutions for your fresh produce needs</p>
            </div>
            
            <div class="row g-4">
                <!-- Production -->
                <div class="col-lg-4 col-md-6">
                    <div class="service-card">
                        <div class="service-icon">
                            <i class="fas fa-seedling"></i>
                        </div>
                        <h4 class="fw-bold mb-3">Production</h4>
                        <p class="text-muted">We expand our potential with a growing lineup of harvesting.</p>
                    </div>
                </div>
                
                <!-- Logistic -->
                <div class="col-lg-4 col-md-6">
                    <div class="service-card">
                        <div class="service-icon">
                            <i class="fas fa-truck"></i>
                        </div>
                        <h4 class="fw-bold mb-3">Logistic</h4>
                        <p class="text-muted">Transportation is not a new field. We're just doing it a little better.</p>
                    </div>
                </div>
                
                <!-- Wholesale -->
                <div class="col-lg-4 col-md-6">
                    <div class="service-card">
                        <div class="service-icon">
                            <i class="fas fa-handshake"></i>
                        </div>
                        <h4 class="fw-bold mb-3">Wholesale</h4>
                        <p class="text-muted">We offer a wide range of fruits and vegetables under sustainable and secure conditions.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>
        <!-- Hero Section -->
    <section id="home" class="hero-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <h1 class="display-4 fw-bold mb-4">Meka Fresh</h1>
                    <h2 class="h3 mb-4">Turkish Fresh Fruits and Vegetables</h2>
                    <p class="lead mb-4">
                        We provide high-quality fruits and vegetables that are sustainably sourced. 
                        The trained crew ensures that orders are fulfilled on time, using only organic 
                        and healthful fruits such as pomegranate and cherry.
                    </p>
                    <a href="#products" class="btn btn-light btn-lg me-3">uyrunler</a>
                    <a href="#contact" class="btn btn-outline-light btn-lg">Contact Us</a>
                </div>
                <div class="col-lg-6 text-center">
                    <img src="<?php echo asset_url('images/hero-fruits.jpg'); ?>" alt="Fresh Fruits" class="img-fluid rounded shadow">
                </div>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="py-5">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <h2 class="display-5 fw-bold text-success mb-4">Asya Fresh</h2>
                    <p class="text-muted mb-4">
                        Asya Fresh Company was created in 2016 and is rapidly growing to become one of the industry's premier businesses. 
                        We have accomplished this via experience with the Samsun port administration and a trained workforce, which has enabled 
                        us to grow our logistic fleet and export volume of fruits and vegetables each year.
                    </p>
                    <p class="text-muted mb-4">
                        We provide high-quality fruits and vegetables that are sustainably sourced. The trained crew ensures that orders are 
                        fulfilled on time, using only organic and healthful fruits such as pomegranate and cherry. As a result, Asya Fresh has 
                        become the region's fastest emerging exporter of fresh fruits and vegetables.
                    </p>
                    <a href="#contact" class="btn btn-success btn-lg">Read more</a>
                </div>
                <div class="col-lg-6 text-center">
                    <img src="<?php echo asset_url('images/company.jpg'); ?>" alt="Asya Fresh Company" class="img-fluid rounded shadow">
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="py-5 bg-light">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="display-5 fw-bold text-success">Request Form</h2>
                <p class="lead text-muted">Get in touch with us for your fresh produce needs</p>
            </div>
            
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="contact-form">
                        <form action="contact-form.php" method="POST" enctype="multipart/form-data">
                            <input type="hidden" name="csrf_token" value="<?php echo get_csrf_token(); ?>">
                            
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="name" class="form-label">Name *</label>
                                    <input type="text" class="form-control" id="name" name="name" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="email" class="form-label">Email *</label>
                                    <input type="email" class="form-control" id="email" name="email" required>
                                </div>
                            </div>
                            
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="product" class="form-label">Products</label>
                                    <select class="form-select" id="product" name="product">
                                        <option value="">Select Product</option>
                                        <option value="Pomegranate">Pomegranate</option>
                                        <option value="Cherry">Cherry</option>
                                        <option value="Nectarine and Peach">Nectarine and Peach</option>
                                        <option value="Pear">Pear</option>
                                        <option value="Pepper Bell">Pepper Bell</option>
                                        <option value="Plum">Plum</option>
                                        <option value="Tomatoes Cherry">Tomatoes Cherry</option>
                                        <option value="Cucumber">Cucumber</option>
                                        <option value="Fig">Fig</option>
                                        <option value="Apricot">Apricot</option>
                                        <option value="Orange">Orange</option>
                                        <option value="Lemon">Lemon</option>
                                        <option value="Zucchini">Zucchini</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="country" class="form-label">Country</label>
                                    <select class="form-select" id="country" name="country">
                                        <option value="">Select Country</option>
                                        <option value="Turkey">Turkey</option>
                                        <option value="Germany">Germany</option>
                                        <option value="France">France</option>
                                        <option value="Italy">Italy</option>
                                        <option value="Spain">Spain</option>
                                        <option value="Netherlands">Netherlands</option>
                                        <option value="United Kingdom">United Kingdom</option>
                                        <option value="Russia">Russia</option>
                                        <option value="United States">United States</option>
                                        <option value="China">China</option>
                                        <option value="Japan">Japan</option>
                                        <option value="Australia">Australia</option>
                                        <option value="Canada">Canada</option>
                                        <option value="Brazil">Brazil</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="file" class="form-label">Add file</label>
                                <input type="file" class="form-control" id="file" name="file" accept=".pdf,.doc,.docx,.jpg,.jpeg,.png">
                                <div class="form-text">Supported formats: PDF, DOC, DOCX, JPG, JPEG, PNG</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="message" class="form-label">Message</label>
                                <textarea class="form-control" id="message" name="message" rows="4" placeholder="Your message here..."></textarea>
                            </div>
                            
                            <div class="text-center">
                                <button type="submit" class="btn btn-success btn-lg px-5">Send</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- News Section -->
    <section id="news" class="py-5">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="display-5 fw-bold text-success">Latest News</h2>
                <p class="lead text-muted">Stay updated with our latest developments</p>
            </div>
            
            <div class="row g-4">
                <!-- News 1 -->
                <div class="col-lg-6">
                    <div class="card h-100 border-0 shadow">
                        <div class="card-body">
                            <h5 class="card-title fw-bold">Asya Fresh has renewed web site!</h5>
                            <p class="card-text text-muted">We have just renewed our English website…</p>
                            <a href="#" class="btn btn-outline-success">Read More</a>
                        </div>
                    </div>
                </div>
                
                <!-- News 2 -->
                <div class="col-lg-6">
                    <div class="card h-100 border-0 shadow">
                        <div class="card-body">
                            <h5 class="card-title fw-bold">Turkey last year in 2020 increased agricultural production by 7.6%</h5>
                            <p class="card-text text-muted">Turkey ranks first in the world in the production of various plant products - from hazelnuts to cherries, from figs to apricots, from quince to poppy.</p>
                            <a href="#" class="btn btn-outline-success">Read More</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark text-white py-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 mb-4">
                    <div class="d-flex align-items-center mb-3">
                        <img src="<?php echo asset_url('images/favicon1.png'); ?>" alt="Asya Fresh" height="40" class="me-2">
                        <h5 class="mb-0 text-success">Asya Fresh</h5>
                    </div>
                    <p class="text-muted">Turkish Fresh Fruits and Vegetables. High-quality, sustainably sourced produce delivered worldwide.</p>
                </div>

                <div class="col-lg-3 col-md-6 mb-4">
                    <h6 class="text-uppercase fw-bold mb-3">Quick Links</h6>
                    <ul class="list-unstyled">
                        <li><a href="#home" class="text-muted text-decoration-none">Home</a></li>
                        <li><a href="#products" class="text-muted text-decoration-none">Products</a></li>
                        <li><a href="#services" class="text-muted text-decoration-none">Services</a></li>
                        <li><a href="#about" class="text-muted text-decoration-none">About</a></li>
                        <li><a href="#contact" class="text-muted text-decoration-none">Contact</a></li>
                    </ul>
                </div>

                <div class="col-lg-3 col-md-6 mb-4">
                    <h6 class="text-uppercase fw-bold mb-3">Products</h6>
                    <ul class="list-unstyled text-muted">
                        <li>Pomegranate</li>
                        <li>Cherry</li>
                        <li>Nectarine & Peach</li>
                        <li>Citrus Fruits</li>
                        <li>Vegetables</li>
                    </ul>
                </div>

                <div class="col-lg-2 col-md-6 mb-4">
                    <h6 class="text-uppercase fw-bold mb-3">Follow Us</h6>
                    <div class="d-flex">
                        <a href="#" class="text-muted me-3 fs-4"><i class="fab fa-facebook"></i></a>
                        <a href="#" class="text-muted me-3 fs-4"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="text-muted me-3 fs-4"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="text-muted fs-4"><i class="fab fa-linkedin"></i></a>
                    </div>
                </div>
            </div>

            <hr class="my-4">

            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0 text-muted">&copy; <?php echo date('Y'); ?> Asya Fresh. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0 text-muted">Designed with <i class="fas fa-heart text-danger"></i> for fresh produce</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- AOS Animation Library -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">

    <!-- Custom JS -->
    <script>
        // Initialize AOS animations
        AOS.init({
            duration: 800,
            easing: 'ease-in-out',
            once: true,
            offset: 100
        });

        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    const headerOffset = 80;
                    const elementPosition = target.getBoundingClientRect().top;
                    const offsetPosition = elementPosition + window.pageYOffset - headerOffset;

                    window.scrollTo({
                        top: offsetPosition,
                        behavior: 'smooth'
                    });
                }
            });
        });

        // Modern navbar scroll effects
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.modern-navbar');
            const scrollProgress = document.querySelector('.scroll-progress');

            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }

            // Update scroll progress bar
            const scrolled = (window.scrollY / (document.documentElement.scrollHeight - window.innerHeight)) * 100;
            scrollProgress.style.width = scrolled + '%';
        });

        // Gallery slideshow functionality
        let currentSlideIndex = 0;
        const slides = document.querySelectorAll('.gallery-slide');
        const indicators = document.querySelectorAll('.indicator');

        function showSlide(index) {
            slides.forEach((slide, i) => {
                slide.classList.toggle('active', i === index);
            });
            indicators.forEach((indicator, i) => {
                indicator.classList.toggle('active', i === index);
            });
        }

        function changeSlide(direction) {
            currentSlideIndex = (currentSlideIndex + direction + slides.length) % slides.length;
            showSlide(currentSlideIndex);
        }

        function currentSlide(index) {
            currentSlideIndex = index - 1;
            showSlide(currentSlideIndex);
        }

        // Auto-play slideshow with pause on hover
        let slideInterval = setInterval(() => {
            changeSlide(1);
        }, 5000);

        // Pause slideshow on hover
        const galleryContainer = document.querySelector('.gallery-container');
        if (galleryContainer) {
            galleryContainer.addEventListener('mouseenter', () => {
                clearInterval(slideInterval);
            });

            galleryContainer.addEventListener('mouseleave', () => {
                slideInterval = setInterval(() => {
                    changeSlide(1);
                }, 5000);
            });
        }

        // Intersection Observer for scroll animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('fade-in-up');
                }
            });
        }, observerOptions);

        // Observe elements for animations
        document.addEventListener('DOMContentLoaded', () => {
            const animateElements = document.querySelectorAll('.modern-product-card, .service-card');
            animateElements.forEach(el => observer.observe(el));
        });

        // Add loading shimmer effect for images
        document.querySelectorAll('img').forEach(img => {
            img.addEventListener('load', function() {
                this.classList.remove('loading-shimmer');
            });

            if (!img.complete) {
                img.classList.add('loading-shimmer');
            }
        });

        // Enhanced mobile menu toggle
        const mobileToggler = document.querySelector('.modern-toggler');
        if (mobileToggler) {
            mobileToggler.addEventListener('click', function() {
                this.classList.toggle('collapsed');
            });
        }
    </script>
</body>
</html>

    