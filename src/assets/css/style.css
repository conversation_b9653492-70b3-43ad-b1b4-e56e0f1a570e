/* <PERSON><PERSON>zel CSS */

:root {
    --primary-color: #1a4c3e;
    --secondary-color: #2d7a5b;
    --accent-color: #4ade80;
    --text-dark: #1f2937;
    --text-light: #6b7280;
    --bg-light: #f9fafb;
    --white: #ffffff;
    --surface: #ffffff;
    --border-light: #e5e7eb;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    --gradient-primary: linear-gradient(135deg, #1a4c3e 0%, #2d7a5b 100%);
    --gradient-accent: linear-gradient(135deg, #4ade80 0%, #22c55e 100%);
    --border-radius: 12px;
    --border-radius-lg: 16px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Modern Base Styles */
* {
    box-sizing: border-box;
}

html {
    overflow-x: hidden;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    color: var(--text-dark);
    line-height: 1.7;
    font-weight: 400;
    margin: 0;
    padding: 0;
    scroll-behavior: smooth;
    overflow-x: hidden;
    background-color: var(--bg-light);
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
}

h1, h2, h3, h4, h5, h6 {
    font-family: 'Playfair Display', serif;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 0.5em;
}

/* Scroll Progress Bar */
.scroll-progress {
    position: fixed;
    top: 0;
    left: 0;
    width: 0%;
    height: 3px;
    background: var(--gradient-accent);
    z-index: 10000;
    transition: width 0.1s ease;
}

/* Modern Navigation Styles */
.modern-navbar {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--border-light);
    padding: 1rem 0;
    transition: var(--transition);
    z-index: 1000;
}

.modern-navbar.scrolled {
    background: rgba(255, 255, 255, 0.98);
    box-shadow: var(--shadow);
    padding: 0.75rem 0;
}

.modern-brand {
    display: flex;
    align-items: center;
    text-decoration: none;
    font-weight: 700;
    font-size: 1.5rem;
    color: var(--text-dark);
    transition: var(--transition);
}

.brand-icon {
    width: 48px;
    height: 48px;
    background: var(--gradient-primary);
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    color: white;
    font-size: 1.2rem;
    transition: var(--transition);
}

.modern-brand:hover .brand-icon {
    transform: scale(1.1) rotate(5deg);
}

.brand-text {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.navbar-nav .nav-link {
    color: var(--text-dark);
    font-weight: 500;
    margin: 0 8px;
    padding: 8px 16px;
    border-radius: var(--border-radius);
    transition: var(--transition);
    position: relative;
}

.navbar-nav .nav-link:hover {
    color: var(--primary-color);
    background: rgba(74, 222, 128, 0.1);
}

.navbar-nav .nav-link.contact-btn {
    background: var(--gradient-primary);
    color: white;
    margin-left: 16px;
}

.navbar-nav .nav-link.contact-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow);
    color: white;
}

/* Modern Toggler */
.modern-toggler {
    border: none;
    padding: 8px;
    background: transparent;
    position: relative;
    width: 30px;
    height: 30px;
}

.modern-toggler span {
    display: block;
    width: 22px;
    height: 2px;
    background: var(--text-dark);
    margin: 5px 0;
    transition: var(--transition);
    border-radius: 2px;
}

.modern-toggler:not(.collapsed) span:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
}

.modern-toggler:not(.collapsed) span:nth-child(2) {
    opacity: 0;
}

.modern-toggler:not(.collapsed) span:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
}

.border-bottom {
    border-color: #e9ecef !important;
}

/* Video Hero Styles - REMOVED (only using images now) */

/* Responsive Design */
@media (max-width: 768px) {
    .image-hero-section {
        padding: 40px 0;
        text-align: center;
    }
    
    .service-card {
        margin-bottom: 20px;
    }
    
    .main-gallery-img {
        height: 250px;
    }
    
    .gallery-img {
        height: 150px;
    }
}

/* Modern Hero Section */
.hero-section {
    background: var(--gradient-primary);
    color: var(--white);
    padding: 120px 0 100px;
    min-height: 70vh;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="%23ffffff" opacity="0.1"/></pattern></defs><rect width="100%" height="100%" fill="url(%23dots)"/></svg>');
    pointer-events: none;
}

/* Ürün Kartları */
.product-card {
    border: none;
    border-radius: 15px;
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
    overflow: hidden;
    background: var(--surface);
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow);
}

.product-card img {
    height: 200px;
    object-fit: cover;
    width: 100%;
    transition: var(--transition);
}

.product-card:hover img {
    transform: scale(1.05);
}

/* Hizmet Kartları */
.service-card {
    text-align: center;
    padding: 30px 20px;
    border-radius: 10px;
    background: var(--white);
    box-shadow: var(--shadow);
    margin-bottom: 30px;
}

.service-icon {
    font-size: 3rem;
    color: var(--accent-color);
    margin-bottom: 20px;
}

/* İletişim Formu */
.contact-form {
    background: var(--bg-light);
    padding: 40px;
    border-radius: 15px;
}

.form-control:focus {
    border-color: var(--accent-color);
    box-shadow: 0 0 0 0.2rem rgba(127, 176, 105, 0.25);
}

/* Modern Footer */
footer {
    background: var(--gradient-primary);
    color: var(--white);
    padding: 60px 0 30px;
    position: relative;
    overflow: hidden;
}

footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 20"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="%23ffffff" opacity="0.05"/></pattern></defs><rect width="100%" height="100%" fill="url(%23grain)"/></svg>');
    pointer-events: none;
}

/* Modern Section Styles */
.modern-section {
    padding: 100px 0;
    position: relative;
}

.section-header {
    margin-bottom: 80px;
}

.section-subtitle {
    display: inline-block;
    background: var(--gradient-accent);
    color: white;
    padding: 6px 16px;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 16px;
}

.section-title {
    font-size: 3.5rem;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 20px;
    line-height: 1.2;
}

.section-description {
    font-size: 1.125rem;
    color: var(--text-light);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.7;
}

/* Modern Product Cards */
.modern-product-card {
    background: var(--surface);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
    border: 1px solid var(--border-light);
    height: 100%;
    display: flex;
    flex-direction: column;
}

.modern-product-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
}

.product-image {
    position: relative;
    overflow: hidden;
    height: 280px;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.modern-product-card:hover .product-image img {
    transform: scale(1.05);
}

.product-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(26, 76, 62, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition);
}

.modern-product-card:hover .product-overlay {
    opacity: 1;
}

.product-actions {
    display: flex;
    gap: 12px;
}

.action-btn {
    width: 48px;
    height: 48px;
    background: white;
    border: none;
    border-radius: 50%;
    color: var(--primary-color);
    font-size: 1.1rem;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
}

.action-btn:hover {
    transform: scale(1.1);
    background: var(--accent-color);
    color: white;
}

.product-content {
    padding: 24px;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.product-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 8px;
}

.product-variety {
    color: var(--text-light);
    font-size: 0.9rem;
    margin-bottom: 16px;
}

.product-features {
    display: flex;
    gap: 8px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.feature-tag {
    background: rgba(74, 222, 128, 0.1);
    color: var(--primary-color);
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.modern-btn {
    background: var(--gradient-primary);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: var(--border-radius);
    font-weight: 600;
    font-size: 0.9rem;
    cursor: pointer;
    transition: var(--transition);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-top: auto;
}

.modern-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

/* Responsive Design */
@media (max-width: 768px) {
    .modern-section {
        padding: 60px 0;
    }

    .section-title {
        font-size: 2.5rem;
    }

    .section-description {
        font-size: 1rem;
    }

    .modern-navbar {
        padding: 0.5rem 0;
    }

    .brand-icon {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .brand-text {
        font-size: 1.25rem;
    }

    .product-image {
        height: 220px;
    }

    .hero-section {
        padding: 60px 0;
        text-align: center;
    }

    .service-card {
        margin-bottom: 20px;
    }
}

/* Modern Buttons */
.btn-primary {
    background: var(--gradient-primary);
    border: none;
    border-radius: var(--border-radius);
    padding: 12px 32px;
    font-weight: 600;
    transition: var(--transition);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    background: var(--gradient-primary);
}

.btn-outline-primary {
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
    background: transparent;
    border-radius: var(--border-radius);
    padding: 10px 30px;
    font-weight: 600;
    transition: var(--transition);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

/* Gallery Styles */
.gallery-item {
    cursor: pointer;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    overflow: hidden;
}

.gallery-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.2);
}

.gallery-item:hover .gallery-icon {
    opacity: 1 !important;
}

.gallery-img {
    height: 200px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.gallery-item:hover .gallery-img {
    transform: scale(1.05);
}

.gallery-item .card-img-overlay {
    background: rgba(0,0,0,0.3);
    transition: background 0.3s ease;
}

.gallery-item:hover .card-img-overlay {
    background: rgba(0,0,0,0.5);
}

/* Main Gallery Image */
.main-gallery-img {
    height: 400px;
    object-fit: cover;
    width: 100%;
}

/* Image Hero Section */
.image-hero-section .card-img-overlay {
    background: linear-gradient(to top, rgba(0,0,0,0.7), transparent);
}

.image-hero-section .card {
    transition: transform 0.3s ease;
}

.image-hero-section .card:hover {
    transform: translateY(-3px);
}

/* Video Styles - REMOVED (only using images now) */

/* Modal Styles */
.modal-content {
    border-radius: 15px;
    overflow: hidden;
}

.carousel-item img {
    height: 400px;
    object-fit: cover;
}

.carousel-caption {
    background: rgba(0,0,0,0.7);
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 20px;
}

/* Fullscreen Gallery Styles */
.fullscreen-gallery {
    position: relative;
    height: 100vh;
    width: 100vw;
    max-width: 100%;
    overflow: hidden;
    margin: 0;
    padding: 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    box-sizing: border-box;
}

.gallery-container {
    position: relative;
    width: 100%;
    height: 100%;
    max-width: 100vw;
    overflow: hidden;
    box-sizing: border-box;
}

.gallery-slides {
    position: relative;
    width: 100%;
    height: 100%;
}

.gallery-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 1s ease-in-out;
    z-index: 1;
}

.gallery-slide.active {
    opacity: 1;
    z-index: 2;
}

.gallery-slide img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    object-position: center;
    display: block;
    max-width: 100%;
    max-height: 100%;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    box-sizing: border-box;
}

.slide-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(44, 85, 48, 0.7), rgba(74, 124, 89, 0.5));
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 3;
}

.slide-content {
    text-align: center;
    color: white;
    max-width: 600px;
    padding: 0 20px;
}

.slide-content h2 {
    font-size: clamp(2rem, 5vw, 3.5rem);
    font-weight: bold;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
    line-height: 1.2;
}

.slide-content p {
    font-size: clamp(1rem, 2.5vw, 1.25rem);
    margin-bottom: 0;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
    line-height: 1.4;
}

/* Navigation Arrows */
.gallery-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(255, 255, 255, 0.9);
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
    z-index: 4;
    font-size: 1.2rem;
    color: var(--primary-color);
    backdrop-filter: blur(10px);
}

.gallery-nav:hover {
    background: rgba(255, 255, 255, 1);
    transform: translateY(-50%) scale(1.1);
    box-shadow: var(--shadow);
}

.gallery-prev {
    left: 20px;
}

.gallery-next {
    right: 20px;
}

/* Slide Indicators */
.gallery-indicators {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 10px;
    z-index: 4;
}

.indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.5);
    cursor: pointer;
    transition: all 0.3s ease;
}

.indicator.active {
    background: rgba(255, 255, 255, 1);
    transform: scale(1.2);
}

.indicator:hover {
    background: rgba(255, 255, 255, 0.8);
}

/* Responsive Design for Gallery */
@media (max-width: 768px) {
    .fullscreen-gallery {
        height: 80vh;
        width: 100%;
        max-width: 100vw;
    }

    .gallery-slide img {
        padding: 20px;
        object-fit: contain;
        width: calc(100% - 40px);
        height: calc(100% - 40px);
    }

    .slide-content h2 {
        font-size: 1.8rem;
    }

    .slide-content p {
        font-size: 0.9rem;
    }

    .gallery-nav {
        width: 35px;
        height: 35px;
        font-size: 0.9rem;
    }

    .gallery-prev {
        left: 10px;
    }

    .gallery-next {
        right: 10px;
    }

    .gallery-indicators {
        bottom: 15px;
    }

    .indicator {
        width: 8px;
        height: 8px;
    }
}

@media (max-width: 480px) {
    .fullscreen-gallery {
        height: 70vh;
        width: 100%;
        max-width: 100vw;
    }

    .gallery-slide img {
        padding: 15px;
        object-fit: contain;
        width: calc(100% - 30px);
        height: calc(100% - 30px);
    }

    .slide-content h2 {
        font-size: 1.5rem;
    }

    .slide-content p {
        font-size: 0.8rem;
    }

    .slide-content {
        padding: 0 15px;
    }
}

/* Scroll Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* Loading Animation */
.loading-shimmer {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-light);
}

::-webkit-scrollbar-thumb {
    background: var(--gradient-primary);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--secondary-color);
}